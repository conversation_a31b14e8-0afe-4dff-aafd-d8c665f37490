import { HttpError, prisma } from 'wasp/server';
import { type Appointment, type User, type Address, type SProvider } from 'wasp/entities';
import type { GetCustomerAppointments, CancelAppointment, GetPublicProviderById } from 'wasp/server/operations';
import { z } from 'zod';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';
import { Prisma, Role } from '@prisma/client';
import { createNotificationEntry } from '../notifications/operations';
import { getDownloadFileSignedURLFromS3 } from '../file-upload/s3Utils';

// Define the expected context type (adjust based on your actual context structure if needed)
type Context = any; // Use any to avoid type conflicts with generated contexts

// Type annotation inferred by Wasp based on main.wasp definition
export const getCustomerAppointments: GetCustomerAppointments<void, Appointment[]> = async (
  _args: unknown, // No arguments needed
  context: Context
): Promise<Appointment[]> => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  // Optional: Add role check if you want to restrict this query strictly to CUSTOMER role
  // if (context.user.role !== 'CUSTOMER') {
  //   throw new HttpError(403, 'User is not authorized to view customer appointments.');
  // }

  const customerUserId = context.user.id;

  try {
    const appointments = await context.entities.Appointment.findMany({
      where: {
        // Filter appointments linked to the logged-in user via the CustomerFolder
        customerFolder: {
          userId: customerUserId,
        },
      },
      include: {
        // Include details needed for display on the appointments page
        service: {
          include: {
            provider: {
              select: {
                id: true,
                title: true,
                phone: true,
                presentation: true,
                isVerified: true,
                category: {
                  select: {
                    id: true,
                    title: true,
                    description: true,
                  },
                },
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    profilePicture: true
                  }
                },
                logo: true
              }
            }
          }
        }, // Service name, duration, etc. with provider info
        place: {
          select: {
            id: true,
            name: true,
            address: true,
            city: true,
            timezone: true,
            detailedAddress: true,
          },
        },   // Place name, address, etc.
        queue: { // ADDED: Include queue title
            select: { title: true }
        },
        customerFolder: {
          include: {
            // Select provider details via the folder
            provider: { // This is the SProvider relation
              select: { // Use select to pick specific fields from SProvider
                id: true, // Good practice to include ID
                title: true, // Select the scalar field 'title',
                phone: true, // Provider phone number
                presentation: true, // Provider description
                isVerified: true, // Verification status
                category: {
                  select: {
                    id: true,
                    title: true,
                    description: true,
                  },
                },
                user: { // Select specific fields from the related User model
                  select: {
                     id: true,
                     firstName: true,
                     lastName: true,
                     email: true,
                     profilePicture: true // Include provider's profile picture
                  }
                },
                logo: true // Include provider's business logo
              }
            }
          }
        }
      },
      orderBy: {
        expectedAppointmentStartTime: 'asc', // Order by upcoming appointments first
      },
    });

    // Transform appointments to include proper download URLs for images
    const appointmentsWithUrls = await Promise.all(
      appointments.map(async (appointment) => {
        // Helper function to generate download URL safely
        const generateDownloadUrl = async (fileKey: string | null | undefined): Promise<string | null> => {
          if (!fileKey) return null;
          try {
            return await getDownloadFileSignedURLFromS3({ key: fileKey });
          } catch (error) {
            console.warn('Could not generate download URL for key:', fileKey, error);
            return null;
          }
        };

        // Get provider from service (primary source)
        const serviceProvider = (appointment as any).service?.provider;

        // Generate download URLs for service provider images
        let serviceProviderProfilePictureUrl: string | null = null;
        let serviceProviderLogoUrl: string | null = null;

        if (serviceProvider?.user?.profilePicture?.key) {
          serviceProviderProfilePictureUrl = await generateDownloadUrl(serviceProvider.user.profilePicture.key);
        }

        if (serviceProvider?.logo?.key) {
          serviceProviderLogoUrl = await generateDownloadUrl(serviceProvider.logo.key);
        }

        // Get provider from customer folder (backup source)
        const folderProvider = (appointment as any).customerFolder?.provider;

        // Generate download URLs for folder provider images
        let folderProviderProfilePictureUrl: string | null = null;
        let folderProviderLogoUrl: string | null = null;

        if (folderProvider?.user?.profilePicture?.key) {
          folderProviderProfilePictureUrl = await generateDownloadUrl(folderProvider.user.profilePicture.key);
        }

        if (folderProvider?.logo?.key) {
          folderProviderLogoUrl = await generateDownloadUrl(folderProvider.logo.key);
        }

        // Create the transformed appointment with download URLs
        const transformedAppointment = {
          ...appointment,
          service: appointment.service ? {
            ...appointment.service,
            provider: serviceProvider ? {
              ...serviceProvider,
              user: serviceProvider.user ? {
                ...serviceProvider.user,
                profilePicture: serviceProvider.user.profilePicture ? {
                  ...serviceProvider.user.profilePicture,
                  downloadUrl: serviceProviderProfilePictureUrl
                } : null
              } : null,
              logo: serviceProvider.logo ? {
                ...serviceProvider.logo,
                downloadUrl: serviceProviderLogoUrl
              } : null
            } : null
          } : null,
          customerFolder: (appointment as any).customerFolder ? {
            ...(appointment as any).customerFolder,
            provider: folderProvider ? {
              ...folderProvider,
              user: folderProvider.user ? {
                ...folderProvider.user,
                profilePicture: folderProvider.user.profilePicture ? {
                  ...folderProvider.user.profilePicture,
                  downloadUrl: folderProviderProfilePictureUrl
                } : null
              } : null,
              logo: folderProvider.logo ? {
                ...folderProvider.logo,
                downloadUrl: folderProviderLogoUrl
              } : null
            } : null
          } : null
        };

        return transformedAppointment;
      })
    );

    return appointmentsWithUrls;
  } catch (error: any) {
    console.error(`Failed to get appointments for customer ${customerUserId}:`, error);
    throw new HttpError(500, error.message || 'Failed to retrieve appointments.');
  }
}; 

// --- Cancel Appointment Action (No Credit Logic) ---

const cancelAppointmentInputSchema = z.object({
  appointmentId: z.number().int().positive(),
});

type CancelAppointmentData = z.infer<typeof cancelAppointmentInputSchema>;

export const cancelAppointment: CancelAppointment<CancelAppointmentData, Appointment> = async (
  rawArgs: CancelAppointmentData,
  context: Context
): Promise<Appointment> => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  const args = ensureArgsSchemaOrThrowHttpError(cancelAppointmentInputSchema, rawArgs);
  const customerUserId = context.user.id;

  // 1. Find the appointment and verify ownership
  const appointment = await context.entities.Appointment.findUnique({
    where: { id: args.appointmentId },
    include: {
      customerFolder: {
          select: {
              userId: true, // Still need this for ownership check
              provider: { // Include the provider through the folder
                  select: {
                      userId: true // Select the provider's User ID
                  }
              }
          }
      },
      // Include other fields if needed, but status, start/end times are fetched by default
    }
  });

  if (!appointment) {
    throw new HttpError(404, 'Appointment not found.');
  }
  if (appointment.customerFolder?.userId !== customerUserId) {
    throw new HttpError(403, 'You are not authorized to cancel this appointment.');
  }

  // 2. Check if the appointment can be canceled (status and time)
  const cancellableStatuses = ['pending', 'confirmed'];
  if (!cancellableStatuses.includes(appointment.status)) {
    throw new HttpError(400, `Cannot cancel appointment with status: ${appointment.status}.`);
  }
  const now = new Date();
  if (appointment.expectedAppointmentEndTime && new Date(appointment.expectedAppointmentEndTime) < now) {
    throw new HttpError(400, 'Cannot cancel an appointment that has already passed.');
  }

  // 3. Update the appointment status and create history record
  try {
    // Store previous status before update
    const previousStatus = appointment.status;

    const [updatedAppointment] = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
        // Update the appointment
        const updated = await tx.appointment.update({
            where: { id: args.appointmentId },
            data: {
              status: 'canceled',
              canceledAt: new Date(),
            },
            include: {
              service: { select: { title: true } },
              customerFolder: {
                  select: {
                      userId: true, // Customer's user ID
                      provider: { // SProvider
                          select: {
                              userId: true, // Provider's User ID
                              title: true // Provider's business title/name
                          }
                      }
                  }
              },
              // You might also want to include the customer's User details for their name
              // if 'actorName' is desired in provider notifications.
              // For example, if CustomerFolder has a direct relation to User (the customer):
              // customerFolder: { include: { customer: { select { firstName: true, lastName: true }} }}
          }
        });

        // Create the history record
        await tx.appointmentHistory.create({
            data: {
                appointmentId: updated.id,
                changedByUserId: customerUserId,
                previousStatus: previousStatus,
                newStatus: 'canceled',
                previousStartTime: appointment.expectedAppointmentStartTime,
                previousEndTime: appointment.expectedAppointmentEndTime,
                newStartTime: appointment.expectedAppointmentStartTime,
                newEndTime: appointment.expectedAppointmentEndTime,
                changeReason: "Canceled by customer",
                previousMotifId: appointment.serviceId,
                newMotifId: appointment.serviceId,
                previousAgendaId: appointment.queueId ?? 0,
                newAgendaId: appointment.queueId ?? 0,
            }
        });

        // Ensure provider data was included before trying to update credits
        if (!appointment.customerFolder?.provider?.userId) {
             console.error(`Could not find provider user ID for appointment ${args.appointmentId} during cancellation. Skipping credit refund.`);
             // Decide if this should throw an error or just log and continue
             // throw new Error('Internal server error: Provider details missing for credit refund.'); 
        } else {
            // refund the provider credits here
            await tx.user.update({
                where: { id: appointment.customerFolder.provider.userId }, // Correct path to provider's user ID
                data: { credits: { increment: 1 } }
            });

            const serviceTitle = updated.service?.title || 'the service';
            const providerName = updated.customerFolder.provider?.title || 'the provider';
            // Formatting the date/time nicely for the message
            const appointmentDateTime = updated.expectedAppointmentStartTime 
                                      ? new Date(updated.expectedAppointmentStartTime).toLocaleString([], { dateStyle: 'medium', timeStyle: 'short' })
                                      : 'the scheduled time';
            
            // To get the customer's name for the provider's notification:
            // You would need to fetch the User record for the customer if it's not already available.
            // const customerUser = await tx.user.findUnique({ where: {id: updated.customerFolder.userId }});
            // const customerDisplayName = customerUser?.firstName ? `${customerUser.firstName} ${customerUser.lastName || ''}`.trim() : 'A customer';
            // For now, we'll make it generic.

            // Notification for the Customer (who performed the cancellation)
            await createNotificationEntry(tx.notification, {
                userId: updated.customerFolder.userId,
                type: 'APPOINTMENT_CANCELED_BY_YOU_CUSTOMER',
                title: 'Appointment Canceled',
                message: `You have successfully canceled your appointment for ${serviceTitle} with ${providerName} scheduled for ${appointmentDateTime}.`,
                link: `/my-appointments`, // Link to their appointments page
                actorId: updated.customerFolder.userId, // Customer themself is the actor
            });

            // Notification for the Provider
            await createNotificationEntry(tx.notification, {
                userId: updated.customerFolder.provider.userId,
                type: 'APPOINTMENT_CANCELED_BY_CUSTOMER_PROVIDER',
                title: 'Appointment Canceled by Customer',
                message: `A customer has canceled their appointment for ${serviceTitle} scheduled for ${appointmentDateTime}.`,
                link: `/admin/appointments`, // Link to provider's appointment dashboard
                actorId: updated.customerFolder.userId, // Customer is the actor
            });
        }

        return [updated]; // Return the updated appointment in an array for consistency
    });

    // Return the updated appointment data from the transaction result
    return updatedAppointment;

  } catch (error: any) {
    console.error(`Failed to cancel appointment ${args.appointmentId} for user ${customerUserId}:`, error);
    if (error.code === 'P2025') {
      throw new HttpError(404, 'Appointment not found during update (possibly already canceled or deleted).');
    }
    throw new HttpError(500, error.message || 'Failed to cancel appointment.');
  }
}; 

// --- Customer Address Management ---

// Schema for creating/updating an address
const addressInputSchema = z.object({
  address: z.string().min(1, "Address line is required."),
  city: z.string().min(1, "City is required."),
  state: z.string().optional(),
  postalCode: z.string().min(1, "Postal code is required."),
  country: z.string().min(1, "Country is required.").default("Algeria"),
  latitude: z.number({ coerce: true }),
  longitude: z.number({ coerce: true }),
  description: z.string().optional().nullable(),
  isPrimary: z.boolean().default(false),
});

type AddressInput = z.infer<typeof addressInputSchema>;

// --- Get Customer Addresses Query ---
export const getCustomerAddresses = async (_args: unknown, context: Context): Promise<Address[]> => {
  if (!context.user?.id || context.user.role !== Role.CUSTOMER) {
    throw new HttpError(401, "User not authenticated or not a customer.");
  }
  return context.entities.Address.findMany({
    where: { userId: context.user.id },
    orderBy: { isPrimary: 'desc' }, // Show primary address first
  });
};

// --- Create Customer Address Action ---
export const createCustomerAddress = async (rawArgs: AddressInput, context: Context): Promise<Address> => {
  if (!context.user?.id || context.user.role !== Role.CUSTOMER) {
    throw new HttpError(401, "User not authenticated or not a customer.");
  }
  const args = ensureArgsSchemaOrThrowHttpError(addressInputSchema, rawArgs);

  // If setting this address as primary, unset other primary addresses for this user
  if (args.isPrimary) {
    await prisma.address.updateMany({
      where: { userId: context.user.id, isPrimary: true },
      data: { isPrimary: false },
    });
  }

  return context.entities.Address.create({
    data: {
      ...args,
      userId: context.user.id,
    },
  });
};

// --- Update Customer Address Action ---
const updateAddressInputSchema = addressInputSchema.extend({
  addressId: z.number().int().positive("Address ID is required."),
});
type UpdateAddressInput = z.infer<typeof updateAddressInputSchema>;

export const updateCustomerAddress = async (rawArgs: UpdateAddressInput, context: Context): Promise<Address> => {
  if (!context.user?.id || context.user.role !== Role.CUSTOMER) {
    throw new HttpError(401, "User not authenticated or not a customer.");
  }
  const args = ensureArgsSchemaOrThrowHttpError(updateAddressInputSchema, rawArgs);

  // Verify the address belongs to the user
  const existingAddress = await context.entities.Address.findFirst({
    where: { id: args.addressId, userId: context.user.id },
  });
  if (!existingAddress) {
    throw new HttpError(404, "Address not found or does not belong to the user.");
  }

  // If setting this address as primary, unset other primary addresses for this user
  if (args.isPrimary && !existingAddress.isPrimary) { // Only run if changing to primary
    await prisma.address.updateMany({
      where: { userId: context.user.id, isPrimary: true, NOT: { id: args.addressId } },
      data: { isPrimary: false },
    });
  }

  const { addressId, ...updateData } = args;
  return context.entities.Address.update({
    where: { id: addressId },
    data: updateData,
  });
};

// --- Delete Customer Address Action ---
const deleteAddressInputSchema = z.object({
  addressId: z.number().int().positive("Address ID is required."),
});
type DeleteAddressInput = z.infer<typeof deleteAddressInputSchema>;

export const deleteCustomerAddress = async (rawArgs: DeleteAddressInput, context: Context): Promise<Address> => {
  if (!context.user?.id || context.user.role !== Role.CUSTOMER) {
    throw new HttpError(401, "User not authenticated or not a customer.");
  }
  const args = ensureArgsSchemaOrThrowHttpError(deleteAddressInputSchema, rawArgs);

  // Verify the address belongs to the user
  const addressToDelete = await context.entities.Address.findFirst({
    where: { id: args.addressId, userId: context.user.id },
  });
  if (!addressToDelete) {
    throw new HttpError(404, "Address not found or does not belong to the user.");
  }

  // Prevent deleting the primary address if it's the only one? (Optional business logic)
  // if (addressToDelete.isPrimary) {
  //   const count = await context.entities.Address.count({ where: { userId: context.user.id } });
  //   if (count <= 1) {
  //     throw new HttpError(400, "Cannot delete the only primary address.");
  //   }
  // }

  return context.entities.Address.delete({
    where: { id: args.addressId },
  });
}; 

// Define a separate context type for public provider operations
type PublicProviderContext = {
  entities: {
    SProvider: any;
    SProvidingPlace: any;
    Service: any;
    ProviderCategory: any;
    User: any;
  };
};

// --- Get Public Provider by ID Query ---
const getPublicProviderByIdInputSchema = z.object({
  providerId: z.number().int().positive("Provider ID is required."),
});

type GetPublicProviderByIdInput = z.infer<typeof getPublicProviderByIdInputSchema>;

export const getPublicProviderById: GetPublicProviderById<GetPublicProviderByIdInput, SProvider> = async (
  rawArgs: GetPublicProviderByIdInput,
  _context: any
): Promise<SProvider> => {
  const args = ensureArgsSchemaOrThrowHttpError(getPublicProviderByIdInputSchema, rawArgs);

  try {
    const provider = await prisma.sProvider.findUnique({
      where: { id: args.providerId },
      include: {
        user: { 
          select: {
            firstName: true,
            lastName: true,
          },
        },
        reviewsReceived: {
          select: {
            rating: true,
            comment: true,
            createdAt: true,
            customer: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        providingPlaces: {
          include: {
            detailedAddress: true,
            queues:true
          },
        },
        services: { 
          include: {
            category: true,
          }
        },
        category: {
          select: {
            title: true,
          }
        }
      },
    });

    if (!provider) {
      throw new HttpError(404, `Provider with ID ${args.providerId} not found.`);
    }

    return provider as SProvider;
  } catch (error: any) {
    console.error(`Failed to get public provider data for ID ${args.providerId}:`, error);
    if (error instanceof HttpError) {
      throw error;
    }
    throw new HttpError(500, error.message || 'Failed to retrieve provider information.');
  }
};

// --- Favorites Management ---

const addFavoriteInputSchema = z.object({
  providerId: z.number().int().positive("Provider ID is required."),
});

type AddFavoriteInput = z.infer<typeof addFavoriteInputSchema>;

export const addFavorite = async (rawArgs: AddFavoriteInput, context: Context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  const args = ensureArgsSchemaOrThrowHttpError(addFavoriteInputSchema, rawArgs);
  const customerId = context.user.id;

  try {
    // Check if provider exists
    const provider = await context.entities.SProvider.findUnique({
      where: { id: args.providerId },
    });

    if (!provider) {
      throw new HttpError(404, 'Provider not found.');
    }

    // Check if favorite already exists
    const existingFavorite = await context.entities.Favorite.findUnique({
      where: {
        customerId_providerId: {
          customerId,
          providerId: args.providerId,
        },
      },
    });

    if (existingFavorite) {
      throw new HttpError(409, 'Provider is already in favorites.');
    }

    // Create favorite
    const favorite = await context.entities.Favorite.create({
      data: {
        customerId,
        providerId: args.providerId,
      },
      include: {
        provider: {
          select: {
            id: true,
            title: true,
            phone: true,
            presentation: true,
            isVerified: true,
            category: {
              select: {
                id: true,
                title: true,
              },
            },
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
            logo: true,
          },
        },
      },
    });

    return favorite;
  } catch (error: any) {
    console.error(`Failed to add favorite for user ${customerId}:`, error);
    if (error instanceof HttpError) {
      throw error;
    }
    throw new HttpError(500, error.message || 'Failed to add favorite.');
  }
};

const removeFavoriteInputSchema = z.object({
  providerId: z.number().int().positive("Provider ID is required."),
});

type RemoveFavoriteInput = z.infer<typeof removeFavoriteInputSchema>;

export const removeFavorite = async (rawArgs: RemoveFavoriteInput, context: Context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  const args = ensureArgsSchemaOrThrowHttpError(removeFavoriteInputSchema, rawArgs);
  const customerId = context.user.id;

  try {
    // Find and delete favorite
    const favorite = await context.entities.Favorite.findUnique({
      where: {
        customerId_providerId: {
          customerId,
          providerId: args.providerId,
        },
      },
    });

    if (!favorite) {
      throw new HttpError(404, 'Favorite not found.');
    }

    await context.entities.Favorite.delete({
      where: {
        customerId_providerId: {
          customerId,
          providerId: args.providerId,
        },
      },
    });

    return { success: true, message: 'Favorite removed successfully.' };
  } catch (error: any) {
    console.error(`Failed to remove favorite for user ${customerId}:`, error);
    if (error instanceof HttpError) {
      throw error;
    }
    throw new HttpError(500, error.message || 'Failed to remove favorite.');
  }
};

export const getFavorites = async (_args: unknown, context: Context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  const customerId = context.user.id;

  try {
    const favorites = await context.entities.Favorite.findMany({
      where: { customerId },
      include: {
        provider: {
          select: {
            id: true,
            title: true,
            phone: true,
            presentation: true,
            isVerified: true,
            category: {
              select: {
                id: true,
                title: true,
              },
            },
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
            logo: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return favorites;
  } catch (error: any) {
    console.error(`Failed to get favorites for user ${customerId}:`, error);
    throw new HttpError(500, error.message || 'Failed to retrieve favorites.');
  }
};

const checkIsFavoriteInputSchema = z.object({
  providerId: z.number().int().positive("Provider ID is required."),
});

type CheckIsFavoriteInput = z.infer<typeof checkIsFavoriteInputSchema>;

export const checkIsFavorite = async (rawArgs: CheckIsFavoriteInput, context: Context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  const args = ensureArgsSchemaOrThrowHttpError(checkIsFavoriteInputSchema, rawArgs);
  const customerId = context.user.id;

  try {
    // Check if the provider exists
    const provider = await context.entities.SProvider.findUnique({
      where: { id: args.providerId },
    });

    if (!provider) {
      throw new HttpError(404, 'Provider not found.');
    }

    // Check if favorite exists
    const favorite = await context.entities.Favorite.findUnique({
      where: {
        customerId_providerId: {
          customerId,
          providerId: args.providerId,
        },
      },
    });

    return { isFavorite: !!favorite };
  } catch (error: any) {
    console.error(`Failed to check favorite status for user ${customerId} and provider ${args.providerId}:`, error);
    if (error instanceof HttpError) {
      throw error;
    }
    throw new HttpError(500, error.message || 'Failed to check favorite status.');
  }
};