import { z } from 'zod';

// Admin login validation
export const AdminLoginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

// Provider management validation
export const GetProvidersQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  isVerified: z.enum(['true', 'false']).optional().transform(val => val === 'true'),
  search: z.string().optional(),
});

export const UpdateProviderStatusSchema = z.object({
  isVerified: z.boolean(),
  reason: z.string().optional(),
});

// Customer management validation
export const GetCustomersQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  search: z.string().optional(),
});

// Admin user creation validation
export const CreateAdminUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  mobileNumber: z.string().optional(),
});

// Provider category validation
export const GetCategoriesQuerySchema = z.object({
  includeHierarchy: z.enum(['true', 'false']).optional().transform(val => val === 'true'),
});

export const CreateCategorySchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  parentId: z.coerce.number().int().positive().optional(),
});

export const UpdateCategorySchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long').optional(),
  parentId: z.coerce.number().int().positive().optional(),
});

export const DeleteCategorySchema = z.object({
  id: z.coerce.number().int().positive(),
});

// Route parameter validation
export const ProviderIdSchema = z.object({
  providerId: z.coerce.number().int().positive(),
});

export const CategoryIdSchema = z.object({
  categoryId: z.coerce.number().int().positive(),
});

// Advertisement validation schemas
export const GetAdvertisementsQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  isActive: z.enum(['true', 'false']).optional().transform(val => val === 'true'),
  search: z.string().optional(),
});

export const CreateAdvertisementSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  subtitle: z.string().max(255, 'Subtitle too long').optional(),
  description: z.string().max(2000, 'Description too long').optional(),
  callToActionText: z.string().min(1, 'Call to action text is required').max(100, 'Call to action text too long'),
  callToActionLink: z.string(),
  isExternal: z.boolean().optional().default(false),
  isActive: z.boolean().optional().default(true),
  sortOrder: z.coerce.number().int().min(0).optional().default(0),
  backgroundImageId: z.string().uuid('Invalid background image ID').optional(),
  pngImageId: z.string().uuid('Invalid PNG image ID').optional(),
});

export const UpdateAdvertisementSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long').optional(),
  subtitle: z.string().max(255, 'Subtitle too long').optional(),
  description: z.string().max(2000, 'Description too long').optional(),
  callToActionText: z.string().min(1, 'Call to action text is required').max(100, 'Call to action text too long').optional(),
  callToActionLink: z.string().url('Invalid URL format').optional(),
  isExternal: z.boolean().optional(),
  isActive: z.boolean().optional(),
  sortOrder: z.coerce.number().int().min(0).optional(),
  backgroundImageId: z.string().uuid('Invalid background image ID').optional(),
  pngImageId: z.string().uuid('Invalid PNG image ID').optional(),
});

export const DeleteAdvertisementSchema = z.object({
  id: z.coerce.number().int().positive(),
});

// Route parameter validation for advertisements
export const AdvertisementIdSchema = z.object({
  advertisementId: z.coerce.number().int().positive(),
});

// Image upload validation for advertisements
export const UploadImageSchema = z.object({
  fileType: z.enum(['image/jpeg', 'image/png']),
  fileName: z.string().min(1, 'File name is required'),
});