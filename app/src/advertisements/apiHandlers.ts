import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { type Advertisement } from 'wasp/entities';
import { getDownloadFileSignedURLFromS3 } from '../file-upload/s3Utils';

type Context = {
  entities: {
    Advertisement: any;
  };
};

/**
 * GET /api/public/advertisements
 * Get all active advertisements for public consumption
 */
export const handleGetPublicAdvertisements = async (
  req: Request,
  res: Response,
  context: Context
) => {
  try {
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    
    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 50) {
      return res.status(400).json({ 
        success: false,
        message: 'Invalid pagination parameters. Page must be >= 1, limit must be between 1 and 50.' 
      });
    }

    const skip = (page - 1) * limit;

    // Get only active advertisements
    const [advertisements, totalCount] = await Promise.all([
      context.entities.Advertisement.findMany({
        where: {
          isActive: true,
        },
        include: {
          backgroundImage: {
            select: {
              id: true,
              name: true,
              type: true,
              key: true,
            }
          },
          pngImage: {
            select: {
              id: true,
              name: true,
              type: true,
              key: true,
            }
          },
        },
        orderBy: [
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      context.entities.Advertisement.count({
        where: {
          isActive: true,
        },
      }),
    ]);

    // Transform the data to include proper image URLs and clean structure
    const transformedAdvertisements = await Promise.all(
      advertisements.map(async (ad: any) => {
        // Generate download URLs for images
        let backgroundImageUrl: string | null = null;
        let pngImageUrl: string | null = null;

        if (ad.backgroundImage) {
          try {
            backgroundImageUrl = await getDownloadFileSignedURLFromS3({ key: ad.backgroundImage.key });
          } catch (error) {
            console.warn(`Could not generate download URL for background image ${ad.backgroundImage.id}:`, error);
          }
        }

        if (ad.pngImage) {
          try {
            pngImageUrl = await getDownloadFileSignedURLFromS3({ key: ad.pngImage.key });
          } catch (error) {
            console.warn(`Could not generate download URL for PNG image ${ad.pngImage.id}:`, error);
          }
        }

        return {
          id: ad.id,
          title: ad.title,
          subtitle: ad.subtitle,
          description: ad.description,
          callToActionText: ad.callToActionText,
          callToActionLink: ad.callToActionLink,
          isExternal: ad.isExternal,
          sortOrder: ad.sortOrder,
          createdAt: ad.createdAt,
          backgroundImage: ad.backgroundImage ? {
            id: ad.backgroundImage.id,
            name: ad.backgroundImage.name,
            type: ad.backgroundImage.type,
            url: backgroundImageUrl,
          } : null,
          pngImage: ad.pngImage ? {
            id: ad.pngImage.id,
            name: ad.pngImage.name,
            type: ad.pngImage.type,
            url: pngImageUrl,
          } : null,
        };
      })
    );

    res.status(200).json({
      success: true,
      data: {
        advertisements: transformedAdvertisements,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNextPage: page < Math.ceil(totalCount / limit),
          hasPreviousPage: page > 1,
        }
      }
    });

  } catch (error: any) {
    console.error('Get public advertisements failed:', error);
    
    if (error instanceof HttpError) {
      res.status(error.statusCode).json({ 
        success: false,
        message: error.message 
      });
    } else {
      res.status(500).json({ 
        success: false,
        message: 'Internal server error' 
      });
    }
  }
};
