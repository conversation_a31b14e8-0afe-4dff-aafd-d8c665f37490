import React, { useState, useEffect } from 'react';
import { useQuery, useAction } from 'wasp/client/operations';
import { getFavorites, addFavorite, removeFavorite } from 'wasp/client/operations';
import { getDownloadFileSignedURLFromS3 } from '../../file-upload/s3Utils';

const MyFavoritesPage: React.FC = () => {
  const [favorites, setFavorites] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch favorites on component mount
  useEffect(() => {
    const fetchFavorites = async () => {
      try {
        setLoading(true);
        const result = await getFavorites({});
        setFavorites(result);
      } catch (err: any) {
        setError(err.message || 'Failed to load favorites');
      } finally {
        setLoading(false);
      }
    };

    fetchFavorites();
  }, []);

  const handleRemoveFavorite = async (providerId: number) => {
    try {
      await removeFavorite({ providerId });
      // Remove from local state
      setFavorites(prev => prev.filter(fav => fav.providerId !== providerId));
    } catch (err: any) {
      setError(err.message || 'Failed to remove favorite');
    }
  };

  const generateDownloadUrl = async (fileKey: string | null | undefined): Promise<string | null> => {
    if (!fileKey) return null;
    try {
      return await getDownloadFileSignedURLFromS3({ key: fileKey });
    } catch (error) {
      console.warn('Could not generate download URL for key:', fileKey, error);
      return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your favorites...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Favorite Providers</h1>
          <p className="mt-2 text-gray-600">Manage your favorite service providers</p>
        </div>

        {favorites.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">❤️</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No favorites yet</h3>
            <p className="text-gray-600">Start exploring providers and add them to your favorites!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {favorites.map((favorite) => (
              <div key={favorite.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 font-semibold">
                        {favorite.provider.title.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {favorite.provider.title || 'Unnamed Provider'}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {favorite.provider.category?.title || 'No category'}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => handleRemoveFavorite(favorite.providerId)}
                      className="text-red-500 hover:text-red-700 p-1"
                      title="Remove from favorites"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>

                  <div className="mt-4">
                    <p className="text-sm text-gray-700 line-clamp-3">
                      {favorite.provider.presentation || 'No description available'}
                    </p>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {favorite.provider.isVerified && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Verified
                        </span>
                      )}
                      <span className="text-sm text-gray-500">
                        ⭐ {favorite.provider.averageRating?.toFixed(1) || 'N/A'}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      Added {new Date(favorite.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyFavoritesPage;