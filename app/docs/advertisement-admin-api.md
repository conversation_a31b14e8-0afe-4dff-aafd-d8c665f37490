# Advertisement Admin API Documentation

This document provides comprehensive details about the advertisement management endpoints available to admin users in the Dalti application.

## Overview

The advertisement admin APIs allow administrators to manage advertisements that are displayed to public users. All endpoints require admin authentication and are prefixed with `/api/auth/admin/advertisements`.

**Available Operations:**
- List advertisements with pagination and search
- Create, update, and delete advertisements
- Upload and delete background images
- Upload and delete PNG overlay images

## Authentication

All advertisement admin endpoints require:
- User authentication
- Admin role (`context.user.role === 'ADMIN'`)

## Endpoints

### 1. GET /api/auth/admin/advertisements

Retrieve a paginated list of advertisements with optional filtering and search.

Retrieve a paginated list of advertisements with optional filtering and search.

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | number | 1 | Page number (minimum: 1) |
| `limit` | number | 20 | Items per page (1-100) |
| `isActive` | boolean | - | Filter by active status |
| `search` | string | - | Search in title, subtitle, and description |

#### Success Response (200)

```json
{
  "success": true,
  "data": {
    "advertisements": [
      {
        "id": 1,
        "title": "Sample Advertisement",
        "subtitle": "Optional subtitle",
        "description": "Detailed description",
        "callToActionText": "Learn More",
        "callToActionLink": "https://example.com",
        "isExternal": false,
        "isActive": true,
        "sortOrder": 0,
        "backgroundImageId": "uuid-string",
        "pngImageId": "uuid-string",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "backgroundImage": {
          "id": "uuid",
          "name": "background.jpg",
          "type": "image/jpeg",
          "url": "https://s3-presigned-download-url"
        },
        "pngImage": {
          "id": "uuid",
          "name": "overlay.png",
          "type": "image/png",
          "url": "https://s3-presigned-download-url"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "totalCount": 25,
      "totalPages": 2
    }
  }
}
```

### 2. POST /api/auth/admin/advertisements

Create a new advertisement.

#### Request Body

| Field | Type | Required | Validation |
|-------|------|----------|------------|
| `title` | string | Yes | 1-255 characters |
| `subtitle` | string | No | Max 255 characters |
| `description` | string | No | Max 2000 characters |
| `callToActionText` | string | Yes | 1-100 characters |
| `callToActionLink` | string | Yes | Valid URL format |
| `isExternal` | boolean | No | Default: false |
| `isActive` | boolean | No | Default: true |
| `sortOrder` | number | No | Min: 0, Default: 0 |
| `backgroundImageId` | string | No | Valid UUID of existing file |
| `pngImageId` | string | No | Valid UUID of existing file |

#### Success Response (201)

```json
{
  "success": true,
  "message": "Advertisement created successfully",
  "data": {
    // Full advertisement object with images
  }
}
```

### 3. PUT /api/auth/admin/advertisements/:advertisementId

Update an existing advertisement.

#### URL Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `advertisementId` | number | Positive integer ID of advertisement |

#### Request Body

All fields are optional. Same validation as create endpoint.

#### Success Response (200)

```json
{
  "success": true,
  "message": "Advertisement updated successfully",
  "data": {
    // Updated advertisement object
  }
}
```

### 4. DELETE /api/auth/admin/advertisements/:advertisementId

Delete an advertisement.

#### URL Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `advertisementId` | number | Positive integer ID of advertisement |

#### Success Response (200)

```json
{
  "success": true,
  "message": "Advertisement deleted successfully"
}
```

### 5. POST /api/auth/admin/advertisements/:advertisementId/background-image

Generate upload URL for advertisement background image.

#### URL Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `advertisementId` | number | Positive integer ID of advertisement |

#### Request Body

```json
{
  "fileType": "image/jpeg" | "image/png",
  "fileName": "string"
}
```

#### Success Response (200)

```json
{
  "success": true,
  "message": "Advertisement background image upload URL generated successfully",
  "data": {
    "uploadUrl": "https://s3-presigned-url",
    "uploadFields": {},
    "file": {
      "id": "uuid",
      "name": "filename.jpg",
      "type": "image/jpeg",
      "key": "s3-key"
    },
    "advertisement": {
      "id": 1,
      "backgroundImageId": "uuid",
      "pngImageId": null
    }
  }
}
```

### 6. POST /api/auth/admin/advertisements/:advertisementId/png-image

Generate upload URL for advertisement PNG overlay image.

Same structure as background image upload endpoint.

### 7. DELETE /api/auth/admin/advertisements/:advertisementId/png-image

Delete the PNG overlay image from an advertisement.

#### URL Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `advertisementId` | number | Positive integer ID of advertisement |

#### Success Response (200)

```json
{
  "success": true,
  "message": "Advertisement PNG image deleted successfully",
  "data": {
    "advertisement": {
      "id": 1,
      "backgroundImageId": "uuid",
      "pngImageId": null
    }
  }
}
```

#### Error Responses

- **400**: Advertisement does not have a PNG image to delete
- **404**: Advertisement not found

### 8. DELETE /api/auth/admin/advertisements/:advertisementId/background-image

Delete the background image from an advertisement.

#### URL Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `advertisementId` | number | Positive integer ID of advertisement |

#### Success Response (200)

```json
{
  "success": true,
  "message": "Advertisement background image deleted successfully",
  "data": {
    "advertisement": {
      "id": 1,
      "backgroundImageId": null,
      "pngImageId": "uuid"
    }
  }
}
```

#### Error Responses

- **400**: Advertisement does not have a background image to delete
- **404**: Advertisement not found

## Error Responses

### Authentication Error (403)

```json
{
  "error": "Admin access required"
}
```

### Validation Error (400)

```json
{
  "success": false,
  "error": "Invalid request data",
  "details": [
    {
      "code": "too_small",
      "message": "Title must be at least 1 character",
      "path": ["title"]
    }
  ]
}
```

### Not Found (404)

```json
{
  "success": false,
  "error": "Advertisement not found"
}
```

### Invalid Image ID (400)

```json
{
  "success": false,
  "error": "Background image not found"
}
```

### Server Error (500)

```json
{
  "success": false,
  "error": "Failed to create advertisement"
}
```

## Business Logic

### Image Management

- Image IDs must reference existing File records in the database
- Images are validated before advertisement creation/update
- Upload endpoints generate S3 presigned URLs for direct cloud storage upload
- **Both public and admin advertisement APIs return S3 presigned download URLs** (not upload URLs) for secure access
- Background images and PNG overlays are stored separately
- Image deletion only removes database references (actual S3 cleanup may be handled separately)

### Sorting and Display

- `sortOrder` determines advertisement display priority (lower numbers first)
- `isActive` controls whether advertisement is shown to users
- `isExternal` indicates if call-to-action link opens in new tab

### Search Functionality

- Searches across `title`, `subtitle`, and `description` fields
- Case-insensitive partial matching

### Pagination

- Default page size: 20 items
- Maximum page size: 100 items
- Page numbering starts at 1

## Related Files

- **Handler**: `app/src/admin/apiHandlers.ts`
- **Validation**: `app/src/admin/validation.ts`
- **Wasp Config**: `app/main.wasp`
- **Entity**: Advertisement (Prisma model)

## Testing

Use the admin credentials to test these endpoints:
- **Email**: `<EMAIL>`
- **Password**: `Dadanasro07`